import os
import cv2
import numpy as np
import subprocess
import json
import time
import glob
import shutil
from shutil import copy2
from tqdm import tqdm
from collections import defaultdict
from datetime import timedelta
import ffmpeg

# GPU imports - simplified
try:
    import torch
    import torch.nn.functional as F
    GPU_AVAILABLE = torch.cuda.is_available()
    DEVICE = 'cuda' if GPU_AVAILABLE else 'cpu'
    TORCH_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False
    DEVICE = 'cpu'
    TORCH_AVAILABLE = False


def format_time(seconds):
    return str(timedelta(seconds=int(seconds))).zfill(8)


def save_group_representative_images(groups, metadata, first_frames_dir, output_dir):
    os.makedirs(output_dir, exist_ok=True)
    metadata_dict = {clip["clip"]: clip for clip in metadata}
    print("\n📸 Saving representative frames for each cluster group...\n")

    for group_idx, group in enumerate(tqdm(groups, desc="Saving group images", unit="group")):
        if not group:
            continue

        # Sort group by clip number
        group_clips = sorted(
            [metadata_dict[c["clip"] if isinstance(c, dict) else c] for c in group],
            key=lambda x: x["clip_no"]
        )

        first_clip = group_clips[0]
        last_clip = group_clips[-1]

        # Aggregate details
        start_frame = first_clip["start_frame"]
        end_frame = last_clip["end_frame"]

        # ✅ Exact total frames (sum of all clip frame counts)
        total_frames = sum(clip["end_frame"] - clip["start_frame"] for clip in group_clips)

        start_time_sec = first_clip["start_time"]
        end_time_sec = last_clip["start_time"] + last_clip["duration"]
        total_duration = sum(clip["duration"] for clip in group_clips)

        start_time_hms = str(timedelta(seconds=start_time_sec)).replace(":", "-").split(".")[0]
        end_time_hms = str(timedelta(seconds=end_time_sec)).replace(":", "-").split(".")[0]

        # Representative frame
        image_path = os.path.join(first_frames_dir, first_clip["first_frame_image"])
        frame = cv2.imread(image_path)
        if frame is None:
            print(f"❌ Failed to read image: {image_path}")
            continue

        # Filename
        filename = (
            f"clip{first_clip['clip_no']:04d}_{start_frame:06d}_{end_frame:06d}_"
            f"{total_frames}f_{total_duration:.2f}s_{start_time_hms}_to_{end_time_hms}.jpg"
        )
        out_path = os.path.join(output_dir, filename)

        # Text lines
        group_clip_nos = [str(clip["clip_no"]) for clip in group_clips]
        text_lines = [
            f"Group Clip No: {first_clip['clip_no']}",
            f"Start Frame: {start_frame}",
            f"End Frame: {end_frame}",
            f"Total Frames: {total_frames}",
            f"Total Duration: {total_duration:.2f} sec",
            f"Start Time: {str(timedelta(seconds=start_time_sec)).split('.')[0]}",
            f"End Time: {str(timedelta(seconds=end_time_sec)).split('.')[0]}",
            f"Clips in Group: {', '.join(group_clip_nos)}"
        ]

        # Draw text on right side
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1.1
        thickness = 2
        line_height = 40
        margin_right = 20
        text_widths = [cv2.getTextSize(line, font, font_scale, thickness)[0][0] for line in text_lines]
        x_right = frame.shape[1] - max(text_widths) - margin_right

        for i, line in enumerate(text_lines):
            y = 40 + i * line_height
            cv2.putText(frame, line, (x_right, y), font, font_scale, (0, 255, 0), thickness)

        if cv2.imwrite(out_path, frame):
            tqdm.write(f"✅ Saved: {out_path}")
        else:
            tqdm.write(f"❌ Failed to save: {out_path}")


def calculate_histogram_diff(frame1, frame2):
    hsv1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2HSV)
    hsv2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2HSV)
    hist1 = cv2.calcHist([hsv1], [0, 1], None, [50, 60], [0, 180, 0, 256])
    hist2 = cv2.calcHist([hsv2], [0, 1], None, [50, 60], [0, 180, 0, 256])
    cv2.normalize(hist1, hist1)
    cv2.normalize(hist2, hist2)
    return 1 - cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)

def extract_clip(input_video, start_time, duration, output_path):
    try:
        (
            ffmpeg
            .input(input_video, ss=start_time)
            .output(output_path, t=duration, map='0:v', c='copy', y=None)
            .overwrite_output()
            .run(quiet=True)
        )
    except ffmpeg.Error as e:
        print(f"❌ FFmpeg error extracting clip: {e.stderr.decode() if e.stderr else e}")

def extract_clip_original(input_video, start_time, duration, output_path):
    try:
        (
            ffmpeg
            .input(input_video, ss=start_time)
            .output(output_path, t=duration, c='copy', y=None)
            .overwrite_output()
            .run(quiet=True)
        )
    except ffmpeg.Error as e:
        print(f"❌ FFmpeg error extracting clip: {e.stderr.decode() if e.stderr else e}")

def save_first_frame(video_path, frame_number, output_path, clip_number, start_frame, end_frame, duration):
    cap = cv2.VideoCapture(video_path)
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
    ret, frame = cap.read()
    if ret:
        font = cv2.FONT_HERSHEY_SIMPLEX
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        text_lines = [
            f"Clip {clip_number}",
            f"Start: {start_frame}",
            f"End: {end_frame}",
            f"Duration: {duration:.2f}s",
            f"Start Time: {start_frame / cap.get(cv2.CAP_PROP_FPS):.2f}s",
            f"Resolution: {width}x{height}"
        ]
        y0 = 40
        for i, line in enumerate(text_lines):
            y = y0 + i * 40
            cv2.putText(frame, line, (10, y), font, 1.2, (0, 255, 0), 3)
        cv2.imwrite(output_path, frame)
    cap.release()

def cluster_clips_by_histogram(all_metadata, first_frames_dir, cluster_similarity=0.15):
    """
    Clusters clips based on histogram similarity of their first frames.

    Args:
        all_metadata (list): List of clip metadata dicts.
        first_frames_dir (str): Path to folder with first frame images.
        cluster_similarity (float): Threshold for histogram distance to group clips.

    Returns:
        groups (list of lists): Each inner list contains clips grouped as a cluster.
    """
    groups = []
    histograms = []

    pbar = tqdm(all_metadata, desc="📊 Grouping clips", unit="clip")
    for clip in pbar:
        img_path = os.path.join(first_frames_dir, clip["first_frame_image"])
        img = cv2.imread(img_path)
        if img is None:
            continue

        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        hist = cv2.calcHist([hsv], [0, 1], None, [50, 60], [0, 180, 0, 256])
        cv2.normalize(hist, hist)

        placed = False
        for i, group_hist in enumerate(histograms):
            dist = 1 - cv2.compareHist(hist, group_hist, cv2.HISTCMP_CORREL)
            if dist < cluster_similarity:
                groups[i].append(clip)
                # Update group histogram as running average
                groups_count = len(groups[i])
                histograms[i] = (group_hist * (groups_count - 1) + hist) / groups_count
                placed = True
                break

        if not placed:
            groups.append([clip])
            histograms.append(hist)

    pbar.close()
    return groups

def process_video(video_path, clips_dir, first_frames_dir, clip_start_index, video_index, threshold=0.15, min_scene_gap=2):
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    prev_frame = None
    frame_id = 0
    last_scene_frame = 0
    clip_index = clip_start_index
    metadata = []

    video_id = f"vid{video_index + 1:04d}"
    print(f"\n🎮 Processing {os.path.basename(video_path)} ({video_id})")

    # Create progress bar
    pbar = tqdm(total=total_frames, desc="Processing frames", unit="frame")

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        if prev_frame is not None:
            diff = calculate_histogram_diff(prev_frame, frame)
            if diff > threshold and (frame_id - last_scene_frame) > min_scene_gap:
                print("\ndiff: ", diff)
                start_time = last_scene_frame / fps
                duration = (frame_id - last_scene_frame) / fps
                ext = os.path.splitext(video_path)[-1].lower()
                clip_name = f"clip_{clip_index:04d}_{last_scene_frame:06d}_{frame_id:06d}_{duration:.2f}s_{start_time:.2f}s_{width}x{height}_{video_id}{ext}"
                clip_path = os.path.join(clips_dir, clip_name)

                extract_clip(video_path, start_time, duration, clip_path)
                pbar.write(f"✅ Extracted: {clip_name}")

                img_name = clip_name.replace(ext, ".jpg")
                img_path = os.path.join(first_frames_dir, img_name)
                save_first_frame(video_path, last_scene_frame, img_path, clip_index, last_scene_frame, frame_id, duration)

                metadata.append({
                    "clip": clip_name,
                    "clip_no": clip_index,
                    "video_id": video_id,
                    "source_video": os.path.basename(video_path),
                    "start_frame": last_scene_frame,
                    "end_frame": frame_id,
                    "start_time": round(start_time, 3),
                    "duration": round(duration, 3),
                    "first_frame_image": img_name
                })

                clip_index += 1
                last_scene_frame = frame_id

        prev_frame = frame
        frame_id += 1
        pbar.update(1)

    if frame_id > last_scene_frame:
        start_time = last_scene_frame / fps
        duration = (frame_id - last_scene_frame) / fps
        ext = os.path.splitext(video_path)[-1].lower()
        clip_name = f"clip_{clip_index:04d}_{last_scene_frame:06d}_{frame_id:06d}_{duration:.2f}s_{start_time:.2f}s_{width}x{height}_{video_id}{ext}"
        clip_path = os.path.join(clips_dir, clip_name)

        extract_clip(video_path, start_time, duration, clip_path)
        pbar.write(f"✅ Extracted: {clip_name}")

        img_name = clip_name.replace(ext, ".jpg")
        img_path = os.path.join(first_frames_dir, img_name)
        save_first_frame(video_path, last_scene_frame, img_path, clip_index, last_scene_frame, frame_id, duration)

        metadata.append({
            "clip": clip_name,
            "clip_no": clip_index,
            "video_id": video_id,
            "source_video": os.path.basename(video_path),
            "start_frame": last_scene_frame,
            "end_frame": frame_id,
            "start_time": round(start_time, 3),
            "duration": round(duration, 3),
            "first_frame_image": img_name
        })

        clip_index += 1

    pbar.close()
    cap.release()
    return metadata, clip_index

def task2(clips_dir):
    clip_root_index = 1
    parent_dir = os.path.abspath(os.path.join(clips_dir, os.pardir))

    # Load all metadata
    metadata_path = os.path.join(clips_dir, "clips_metadata.json")
    groups_path = os.path.join(clips_dir, "groups.json")

    if not os.path.exists(metadata_path):
        print("❌ clips_metadata.json not found. Run Task 1 first.")
        return

    if not os.path.exists(groups_path):
        print("❌ groups.json not found. Run clustering first.")
        return

    with open(metadata_path, "r") as f:
        metadata = json.load(f)

    with open(groups_path, "r") as f:
        groups = json.load(f)

    # Clip number input
    while True:
        clip_input = input("\n🎯 Enter a single clip number (e.g. 5) or 'exit': ").strip()
        if clip_input.lower() == 'exit':
            print("👋 Exiting Task 2.")
            break

        try:
            clip_number = int(clip_input)
        except ValueError:
            print("⚠️ Invalid input. Please enter a valid clip number.")
            continue

        # Find group containing this clip number
        matched_group = None
        for group in groups:
            if any(clip["clip_no"] == clip_number for clip in group):
                matched_group = group
                break

        if not matched_group:
            print("⚠️ Clip number not found in any group.")
            continue

        # Output folder setup
        clip_folder = os.path.join(parent_dir, f"clip_{clip_root_index:04d}")
        os.makedirs(clip_folder, exist_ok=True)

        shots_dir = os.path.join(clip_folder, "shots")
        comfy_dir = os.path.join(clip_folder, "comfy")
        mov_dir = os.path.join(clip_folder, "mov")
        nuke_dir = os.path.join(clip_folder, "nuke")
        ref_dir = os.path.join(clip_folder, "ref_img")
        scan_dir = os.path.join(clip_folder, "scan_frame")

        os.makedirs(shots_dir, exist_ok=True)
        os.makedirs(comfy_dir, exist_ok=True)
        os.makedirs(os.path.join(mov_dir, "mid"), exist_ok=True)
        os.makedirs(os.path.join(mov_dir, "out"), exist_ok=True)
        os.makedirs(nuke_dir, exist_ok=True)
        os.makedirs(ref_dir, exist_ok=True)
        os.makedirs(scan_dir, exist_ok=True)
        for sub in ["comfy_plate_out", "inbetween", "mask", "ref_img", "workflows"]:
            os.makedirs(os.path.join(comfy_dir, sub), exist_ok=True)

        # Create set of selected clip names from matched group
        selected_clip_names = {clip["clip"] for clip in matched_group}

        print(f"\nCopying {len(selected_clip_names)} clips from group to {shots_dir}...")
        for clip in tqdm(matched_group, desc="Copying clips", unit="clip"):
            source_clip_path = os.path.join(clips_dir, clip["clip"])
            if os.path.exists(source_clip_path):
                copy2(source_clip_path, shots_dir)
                tqdm.write(f"📂 Copied: {clip['clip']} → {shots_dir}")
            else:
                tqdm.write(f"❌ Missing: {clip['clip']} → Expected at: {source_clip_path}")

        clip_root_index += 1


def replace_video_frames(mid_path, original_path, output_path):
    command = [
        "ffmpeg", "-y",
        "-i", original_path,           # only needed if you're pulling audio or timecode
        "-i", mid_path,                # final video stream
        "-map", "1:v:0",
        "-c:v", "prores_ks",           # re-encode to ProRes
        "-profile:v", "1",             # 1 = ProRes 422 Standard (apcn)
        "-pix_fmt", "yuv422p10le",
        "-color_primaries", "bt709",
        "-color_trc", "bt709",
        "-colorspace", "bt709",
        "-movflags", "+write_colr",
        output_path
    ]
    result = subprocess.run(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )

    if result.returncode != 0:
        print("❌ FFmpeg failed:")
        print(result.stderr)
    else:
        print("✅ FFmpeg succeeded:")
        print(result.stdout)

def task3(clips_root):
    for folder_name in sorted(os.listdir(clips_root)):
        clip_path = os.path.join(clips_root, folder_name)
        if not os.path.isdir(clip_path) or not folder_name.startswith("clip_"):
            continue

        shots_dir = os.path.join(clip_path, "shots")
        mid_dir = os.path.join(clip_path, "mov", "mid")
        out_dir = os.path.join(clip_path, "mov", "out")
        os.makedirs(out_dir, exist_ok=True)

        for file_name in os.listdir(mid_dir):
            if not file_name.lower().endswith((".mov", ".mp4")):
                continue

            mid_file = os.path.join(mid_dir, file_name)
            base_name = file_name.rsplit("_", 1)[0] + os.path.splitext(file_name)[1]
            original_file = os.path.join(shots_dir, base_name)
            output_file = os.path.join(out_dir, base_name)

            if os.path.exists(original_file):
                print(f"🔁 Replacing frames: {mid_file} → {output_file}")
                replace_video_frames(mid_file, original_file, output_file)
            else:
                print(f"❌ Original not found: {original_file}")


def task4(parent_dir):
    clips_dir = os.path.join(parent_dir, "clips")
    metadata_path = os.path.join(clips_dir, "video_id_map.json")

    if not os.path.exists(metadata_path):
        print("❌ video_id_map.json not found. Run Task 1 first.")
        return

    with open(metadata_path, "r") as f:
        metadata = json.load(f)

    clip_folders = [f for f in sorted(os.listdir(parent_dir))
                    if os.path.isdir(os.path.join(parent_dir, f)) and f.startswith("clip_")]

    print(f"\nProcessing {len(clip_folders)} clip folders for merging...")

    for folder_name in tqdm(clip_folders, desc="Processing folders", unit="folder"):
        clip_path = os.path.join(parent_dir, folder_name)
        out_mov_dir = os.path.join(clip_path, "mov", "out")

        if not os.path.exists(out_mov_dir):
            print(f"⚠️ Skipping {folder_name}: No mov/out directory found")
            continue

        merged_out_dir = os.path.join(clip_path, "out")
        os.makedirs(merged_out_dir, exist_ok=True)
        merged_out_dir_temp = os.path.join(clip_path, "out", "temp")
        os.makedirs(merged_out_dir_temp, exist_ok=True)

        found_ids = set()
        for file_name in os.listdir(out_mov_dir):
            if "vid" in file_name:
                parts = file_name.split("_")
                for part in parts:
                    if part.startswith("vid") and len(part) >= 7:
                        found_ids.add(part[:7])

        if not found_ids:
            print(f"⚠️ No video IDs found in {folder_name}/mov/out")
            continue

        print(f"📊 Found video IDs in {folder_name}: {found_ids}")

        # Process each video ID without nested tqdm to reduce clutter
        for vid_id in found_ids:
            print(f"🔍 Processing {vid_id} in {folder_name}")

            original_clips = []
            edited_clips = []

            # Get original clips
            for clip_name in os.listdir(clips_dir):
                if f"_{vid_id}" in clip_name and clip_name.lower().endswith((".mov", ".mp4")):
                    original_clips.append((clip_name, os.path.join(clips_dir, clip_name)))

            # Get edited clips
            for clip_name in os.listdir(out_mov_dir):
                if f"_{vid_id}" in clip_name and clip_name.lower().endswith((".mov", ".mp4")):
                    edited_clips.append((clip_name, os.path.join(out_mov_dir, clip_name)))

            clip_map = {}

            # Map original clips by clip number
            for clip_name, clip_path in original_clips:
                try:
                    clip_num = int(clip_name.split("_")[1])
                    clip_map[clip_num] = clip_path
                except (IndexError, ValueError) as e:
                    print(f"⚠️ Error parsing clip number from {clip_name}: {e}")

            # Override with edited clips
            for clip_name, clip_path in edited_clips:
                try:
                    clip_num = int(clip_name.split("_")[1])
                    clip_map[clip_num] = clip_path
                except (IndexError, ValueError) as e:
                    print(f"⚠️ Error parsing clip number from {clip_name}: {e}")

            sorted_clips = [(num, path) for num, path in sorted(clip_map.items())]

            if not sorted_clips:
                print(f"⚠️ No clips found for {vid_id} in {folder_name}")
                continue

            temp_list_file = os.path.join(merged_out_dir_temp, f"concat_list_{vid_id}.txt")

            try:
                with open(temp_list_file, "w") as list_file:
                    for _, clip_path in sorted_clips:
                        list_file.write(f"file '{clip_path}'\n")

                output_filename = metadata.get(vid_id, f"{vid_id}.mov")
                merged_output_path = os.path.join(merged_out_dir, output_filename)
                merged_output_path_temp = os.path.join(merged_out_dir_temp, output_filename)
                os.makedirs(os.path.join(merged_out_dir, "temp"), exist_ok=True)

                print(f"🎬 Merging clips for {vid_id}...")

                (
                    ffmpeg
                    .input(temp_list_file, format='concat', safe=0)
                    .output(merged_output_path_temp, c='copy',)
                    .overwrite_output()
                    .run(quiet=True)
                )

                command = [
                    "ffmpeg", "-y", "-i", os.path.join(os.getcwd(), output_filename), "-i", merged_output_path_temp,
                    "-map", "1:v:0", "-map", "0:a?", "-map", "0:s?", "-map_metadata", "0", "-c", "copy", merged_output_path
                ]
                result = subprocess.run(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

                print(f"✅ Merged: {vid_id} → {merged_output_path}")

            except ffmpeg.Error as e:
                print(f"❌ FFmpeg error merging {vid_id}: {e.stderr.decode() if e.stderr else e}")
            except Exception as e:
                print(f"❌ Error processing {vid_id} in {folder_name}: {e}")

            finally:
                if os.path.exists(merged_out_dir_temp):
                    shutil.rmtree(merged_out_dir_temp)



def task5(parent_dir):
    clips_dir = os.path.join(parent_dir, "clips")
    metadata_path = os.path.join(clips_dir, "video_id_map.json")

    if not os.path.exists(metadata_path):
        print("❌ video_id_map.json not found. Run Task 1 first.")
        return

    with open(metadata_path, "r") as f:
        metadata = json.load(f)

    clip_folders = [f for f in sorted(os.listdir(parent_dir))
                    if os.path.isdir(os.path.join(parent_dir, f)) and f.startswith("clip_")]

    print(f"\n📂 Scanning {len(clip_folders)} clip folders for clips...")

    merged_out_dir = os.path.join(parent_dir, "out")
    os.makedirs(merged_out_dir, exist_ok=True)

    # Step 1: Collect all vid_id → {clip_name: full_path} mappings
    video_clips = defaultdict(dict)

    # 1a: From clips_dir
    for file_name in os.listdir(clips_dir):
        if file_name.lower().endswith((".mov", ".mp4")) and "vid" in file_name:
            parts = file_name.split("_")
            for part in parts:
                if part.startswith("vid") and len(part) >= 7:
                    vid_id = part[:7]
                    video_clips[vid_id][file_name] = os.path.join(clips_dir, file_name)

    # 1b: From mov/out directories inside each clip_ folder
    for folder_name in clip_folders:
        out_mov_dir = os.path.join(parent_dir, folder_name, "mov", "out")
        if not os.path.exists(out_mov_dir):
            continue
        for file_name in os.listdir(out_mov_dir):
            if file_name.lower().endswith((".mov", ".mp4")) and "vid" in file_name:
                parts = file_name.split("_")
                for part in parts:
                    if part.startswith("vid") and len(part) >= 7:
                        vid_id = part[:7]
                        video_clips[vid_id][file_name] = os.path.join(out_mov_dir, file_name)

    print(f"🔢 Total unique video IDs to merge: {len(video_clips)}")

    # Step 2: Merge each vid_id group with one tqdm bar
    for vid_id, file_dict in tqdm(video_clips.items(), desc="🎞️ Merging videos", unit="video"):
        merged_out_dir_temp = os.path.join(parent_dir, "out", "temp")
        os.makedirs(merged_out_dir_temp, exist_ok=True)
        temp_list_file = os.path.join(merged_out_dir_temp, f"concat_list_{vid_id}.txt")

        try:
            with open(temp_list_file, "w") as list_file:
                for clip_name in sorted(file_dict.keys()):
                    list_file.write(f"file '{file_dict[clip_name]}'\n")

            output_filename = metadata.get(vid_id, f"{vid_id}.mov")
            merged_output_path = os.path.join(merged_out_dir, output_filename)
            merged_output_path_temp = os.path.join(merged_out_dir_temp, output_filename)

            try:
                ffmpeg.input(temp_list_file, format='concat', safe=0) \
                      .output(merged_output_path_temp, c='copy') \
                      .overwrite_output() \
                      .run(quiet=True)

                print(f"✅ Merged: {vid_id} → {merged_output_path_temp}")

            except ffmpeg.Error as e:
                print(f"❌ FFmpeg error for {vid_id}: {e.stderr.decode() if e.stderr else e}")

            command = [
                        "ffmpeg", "-y", "-i", os.path.join(os.getcwd(), output_filename), "-i", merged_output_path_temp,
                        "-map", "1:v:0", "-map", "0:a?", "-map", "0:s?", "-map_metadata", "0", "-c", "copy", merged_output_path
                    ]
            result = subprocess.run(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

        except Exception as e:
            print(f"❌ Error creating concat list for {vid_id}: {e}")

        finally:
            if os.path.exists(merged_out_dir_temp):
                shutil.rmtree(merged_out_dir_temp)


def task6(parent_dir):
    clips_dir = os.path.join(parent_dir, "clips")
    metadata_path = os.path.join(clips_dir, "video_id_map.json")

    if not os.path.exists(metadata_path):
        print("❌ video_id_map.json not found. Run Task 1 first.")
        return

    with open(metadata_path, "r") as f:
        metadata = json.load(f)

    while True:

        clip_input = input("\n🎯 Enter a single clip folder number (e.g. 2) or 'exit': ").strip()
        if clip_input.lower() == 'exit':
            print("👋 Exiting Task 2.")
            break

        try:
            clip_number = int(clip_input)
        except ValueError:
            print("⚠️ Invalid input. Please enter a valid clip number.")
            continue

        clip_folder = f"clip_{clip_number:04d}"

        clip_folders = [f for f in os.listdir(parent_dir)
                        if os.path.isdir(os.path.join(parent_dir, f)) and f.startswith("clip_") and f==clip_folder]
        
        print(clip_folders)

        print(f"\nProcessing {len(clip_folders)} clip folders for merging...")

        for folder_name in tqdm(clip_folders, desc="Processing folders", unit="folder"):
            clip_path = os.path.join(parent_dir, folder_name)
            out_mov_dir = os.path.join(clip_path, "mov", "out")

            if not os.path.exists(out_mov_dir):
                print(f"⚠️ Skipping {folder_name}: No mov/out directory found")
                continue

            merged_out_dir = os.path.join(clip_path, "out")
            os.makedirs(merged_out_dir, exist_ok=True)
            merged_out_dir_temp = os.path.join(clip_path, "out", "temp")
            os.makedirs(merged_out_dir_temp, exist_ok=True)

            found_ids = set()
            for file_name in os.listdir(out_mov_dir):
                if "vid" in file_name:
                    parts = file_name.split("_")
                    for part in parts:
                        if part.startswith("vid") and len(part) >= 7:
                            found_ids.add(part[:7])

            if not found_ids:
                print(f"⚠️ No video IDs found in {folder_name}/mov/out")
                continue

            print(f"📊 Found video IDs in {folder_name}: {found_ids}")

            # Process each video ID without nested tqdm to reduce clutter
            for vid_id in found_ids:
                print(f"🔍 Processing {vid_id} in {folder_name}")

                original_clips = []
                edited_clips = []

                # Get original clips
                for clip_name in os.listdir(clips_dir):
                    if f"_{vid_id}" in clip_name and clip_name.lower().endswith((".mov", ".mp4")):
                        original_clips.append((clip_name, os.path.join(clips_dir, clip_name)))

                # Get edited clips
                for clip_name in os.listdir(out_mov_dir):
                    if f"_{vid_id}" in clip_name and clip_name.lower().endswith((".mov", ".mp4")):
                        edited_clips.append((clip_name, os.path.join(out_mov_dir, clip_name)))

                clip_map = {}

                # Map original clips by clip number
                for clip_name, clip_path in original_clips:
                    try:
                        clip_num = int(clip_name.split("_")[1])
                        clip_map[clip_num] = clip_path
                    except (IndexError, ValueError) as e:
                        print(f"⚠️ Error parsing clip number from {clip_name}: {e}")

                # Override with edited clips
                for clip_name, clip_path in edited_clips:
                    try:
                        clip_num = int(clip_name.split("_")[1])
                        clip_map[clip_num] = clip_path
                    except (IndexError, ValueError) as e:
                        print(f"⚠️ Error parsing clip number from {clip_name}: {e}")

                sorted_clips = [(num, path) for num, path in sorted(clip_map.items())]

                if not sorted_clips:
                    print(f"⚠️ No clips found for {vid_id} in {folder_name}")
                    continue

                temp_list_file = os.path.join(merged_out_dir_temp, f"concat_list_{vid_id}.txt")

                try:
                    with open(temp_list_file, "w") as list_file:
                        for _, clip_path in sorted_clips:
                            list_file.write(f"file '{clip_path}'\n")

                    output_filename = metadata.get(vid_id, f"{vid_id}.mov")
                    merged_output_path = os.path.join(merged_out_dir, output_filename)
                    merged_output_path_temp = os.path.join(merged_out_dir_temp, output_filename)
                    os.makedirs(os.path.join(merged_out_dir, "temp"), exist_ok=True)

                    print(f"🎬 Merging clips for {vid_id}...")

                    (
                        ffmpeg
                        .input(temp_list_file, format='concat', safe=0)
                        .output(merged_output_path_temp, c='copy',)
                        .overwrite_output()
                        .run(quiet=True)
                    )

                    command = [
                        "ffmpeg", "-y", "-i", os.path.join(os.getcwd(), output_filename), "-i", merged_output_path_temp,
                        "-map", "1:v:0", "-map", "0:a?", "-map", "0:s?", "-map_metadata", "0", "-c", "copy", merged_output_path
                    ]
                    result = subprocess.run(
                        command,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )

                    print(f"✅ Merged: {vid_id} → {merged_output_path}")

                except ffmpeg.Error as e:
                    print(f"❌ FFmpeg error merging {vid_id}: {e.stderr.decode() if e.stderr else e}")
                except Exception as e:
                    print(f"❌ Error processing {vid_id} in {folder_name}: {e}")

                finally:
                    if os.path.exists(merged_out_dir_temp):
                        shutil.rmtree(merged_out_dir_temp)

def gpu_process_frame(frame, overlay, pad=20):
    """GPU-accelerated frame processing"""
    if not GPU_AVAILABLE:
        return cpu_process_frame(frame, overlay, pad)

    try:
        h, w = frame.shape[:2]

        # Convert to tensor and add alpha
        frame_tensor = torch.from_numpy(frame).float().to(DEVICE)
        alpha = torch.full((h, w, 1), 255.0, device=DEVICE)
        frame_bgra = torch.cat([frame_tensor, alpha], dim=2)

        # Add padding
        padded = F.pad(frame_bgra.permute(2, 0, 1), (pad, pad, pad, pad)).permute(1, 2, 0)

        # Resize overlay and blend
        overlay_tensor = torch.from_numpy(overlay).float().to(DEVICE)
        if overlay_tensor.shape[:2] != padded.shape[:2]:
            overlay_tensor = F.interpolate(
                overlay_tensor.permute(2, 0, 1).unsqueeze(0),
                size=padded.shape[:2], mode='bilinear'
            ).squeeze(0).permute(1, 2, 0)

        # Alpha blending
        alpha_overlay = overlay_tensor[:, :, 3:4] / 255.0
        result = padded.clone()
        result[:, :, :3] = (1 - alpha_overlay) * padded[:, :, :3] + alpha_overlay * overlay_tensor[:, :, :3]

        return result[:, :, :3].cpu().numpy().astype(np.uint8)
    except Exception:
        return cpu_process_frame(frame, overlay, pad)

def cpu_process_frame(frame, overlay, pad=20):
    """CPU fallback processing"""
    h, w = frame.shape[:2]
    padded = np.zeros((h + pad * 2, w + pad * 2, 3), dtype=np.uint8)
    padded[pad:pad + h, pad:pad + w] = frame

    # Simple overlay
    overlay_resized = cv2.resize(overlay, (padded.shape[1], padded.shape[0]))
    if overlay_resized.shape[2] == 4:
        alpha = overlay_resized[:, :, 3:4] / 255.0
        for c in range(3):
            padded[:, :, c] = padded[:, :, c] * (1 - alpha[:, :, 0]) + overlay_resized[:, :, c] * alpha[:, :, 0]

    return padded

def is_cuda_available():
    """Legacy function for compatibility"""
    return GPU_AVAILABLE



def add_padding_with_transparency_torch(frame, pad=20, device='cuda'):
    """PyTorch GPU-accelerated padding with transparency"""
    if not TORCH_AVAILABLE or not torch.cuda.is_available():
        return add_padding_with_transparency_cpu(frame, pad)

    try:
        # Convert to torch tensor and move to GPU
        if len(frame.shape) == 3 and frame.shape[2] == 3:
            # Add alpha channel
            alpha = np.full((frame.shape[0], frame.shape[1], 1), 255, dtype=np.uint8)
            frame = np.concatenate([frame, alpha], axis=2)

        # Convert to tensor (H, W, C) -> (C, H, W)
        frame_tensor = torch.from_numpy(frame).permute(2, 0, 1).float().to(device)

        # Add padding
        padded = F.pad(frame_tensor, (pad, pad, pad, pad), mode='constant', value=0)

        # Convert back to numpy (C, H, W) -> (H, W, C)
        result = padded.permute(1, 2, 0).cpu().numpy().astype(np.uint8)
        return result

    except Exception as e:
        print(f"PyTorch padding failed, using CPU: {e}")
        return add_padding_with_transparency_cpu(frame, pad)

def add_padding_with_transparency_cpu(frame, pad=20):
    """CPU fallback for padding"""
    h, w = frame.shape[:2]

    # Add alpha channel if needed
    if frame.shape[2] == 3:
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2BGRA)

    # Efficient padding using numpy
    new_frame = np.zeros((h + pad * 2, w + pad * 2, 4), dtype=np.uint8)
    new_frame[pad:pad + h, pad:pad + w] = frame
    return new_frame

def add_padding_with_transparency(frame, pad=20, use_cuda=False):
    """Main padding function with GPU/CPU selection"""
    if use_cuda and TORCH_AVAILABLE and torch.cuda.is_available():
        return add_padding_with_transparency_torch(frame, pad)
    else:
        return add_padding_with_transparency_cpu(frame, pad)

def overlay_image_alpha_torch(background, overlay, device='cuda'):
    """PyTorch GPU-accelerated alpha blending"""
    if not TORCH_AVAILABLE or not torch.cuda.is_available():
        return overlay_image_alpha_cpu(background, overlay)

    try:
        # Ensure both images have alpha channels
        if background.shape[2] == 3:
            alpha_bg = np.full((background.shape[0], background.shape[1], 1), 255, dtype=np.uint8)
            background = np.concatenate([background, alpha_bg], axis=2)

        if overlay.shape[2] == 3:
            alpha_ov = np.full((overlay.shape[0], overlay.shape[1], 1), 255, dtype=np.uint8)
            overlay = np.concatenate([overlay, alpha_ov], axis=2)

        # Resize overlay if needed
        if overlay.shape[:2] != background.shape[:2]:
            overlay = cv2.resize(overlay, (background.shape[1], background.shape[0]))

        # Convert to torch tensors and move to GPU
        bg_tensor = torch.from_numpy(background).float().to(device) / 255.0
        ov_tensor = torch.from_numpy(overlay).float().to(device) / 255.0

        # Extract alpha channels
        alpha_overlay = ov_tensor[:, :, 3:4]
        alpha_background = 1.0 - alpha_overlay

        # Alpha blending
        result_tensor = torch.zeros_like(bg_tensor)
        result_tensor[:, :, :3] = (alpha_overlay * ov_tensor[:, :, :3] +
                                  alpha_background * bg_tensor[:, :, :3])
        result_tensor[:, :, 3] = bg_tensor[:, :, 3]  # Keep background alpha

        # Convert back to numpy
        result = (result_tensor * 255.0).cpu().numpy().astype(np.uint8)
        return result

    except Exception as e:
        print(f"PyTorch alpha blending failed, using CPU: {e}")
        return overlay_image_alpha_cpu(background, overlay)

def overlay_image_alpha_cpu(background, overlay):
    """CPU fallback for alpha blending"""
    # Resize overlay to match background if needed
    if overlay.shape[:2] != background.shape[:2]:
        overlay = cv2.resize(overlay, (background.shape[1], background.shape[0]))

    # Ensure overlay has alpha channel
    if overlay.shape[2] != 4:
        overlay = cv2.cvtColor(overlay, cv2.COLOR_BGR2BGRA)

    # Ensure background has alpha channel
    if background.shape[2] != 4:
        background = cv2.cvtColor(background, cv2.COLOR_BGR2BGRA)

    # Optimized alpha blending using numpy vectorization
    alpha_overlay = overlay[:, :, 3:4].astype(np.float32) / 255.0
    alpha_background = 1.0 - alpha_overlay

    # Vectorized blending for all color channels at once
    overlay_rgb = overlay[:, :, :3].astype(np.float32)
    background_rgb = background[:, :, :3].astype(np.float32)

    result = np.zeros_like(background, dtype=np.uint8)
    result[:, :, :3] = (alpha_overlay * overlay_rgb + alpha_background * background_rgb).astype(np.uint8)
    result[:, :, 3] = background[:, :, 3]  # retain original alpha

    return result

def overlay_image_alpha(background, overlay, use_cuda=False):
    """Main alpha blending function with GPU/CPU selection"""
    if use_cuda and TORCH_AVAILABLE and torch.cuda.is_available():
        return overlay_image_alpha_torch(background, overlay)
    else:
        return overlay_image_alpha_cpu(background, overlay)

def process_video_overlay(video_path, overlay_img, output_path, use_cuda=False):
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"⚠️ Couldn't open video: {video_path}")
        return

    # Use H.264 codec for better compatibility and performance
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    fps = cap.get(cv2.CAP_PROP_FPS)
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    out_w, out_h = w + 40, h + 40
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    out = cv2.VideoWriter(output_path, fourcc, fps, (out_w, out_h), True)
    if not out.isOpened():
        print(f"⚠️ Couldn't write video: {output_path}")
        return

    print(f"📊 Processing {total_frames} frames at {fps:.2f} FPS")
    print(f"📐 Input: {w}x{h} → Output: {out_w}x{out_h}")

    # Pre-process overlay to match output size for efficiency
    overlay_resized = cv2.resize(overlay_img, (out_w, out_h))
    if overlay_resized.shape[2] != 4:
        overlay_resized = cv2.cvtColor(overlay_resized, cv2.COLOR_BGR2BGRA)

    # Pre-compute alpha values for faster blending
    alpha_overlay = overlay_resized[:, :, 3:4].astype(np.float32) / 255.0
    alpha_background = 1.0 - alpha_overlay
    overlay_rgb = overlay_resized[:, :, :3].astype(np.float32)

    pbar = tqdm(total=total_frames, desc=os.path.basename(video_path), unit="frames")
    frame_count = 0
    processing_start = time.time()

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        if use_cuda:
            try:
                # GPU processing
                gpu_frame = cv2.cuda_GpuMat()
                gpu_frame.upload(frame)
                gpu_frame_bgra = cv2.cuda.cvtColor(gpu_frame, cv2.COLOR_BGR2BGRA)
                frame = gpu_frame_bgra.download()
            except Exception:
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2BGRA)
        else:
            # Optimized CPU processing - direct padding without intermediate conversion
            padded_frame = np.zeros((out_h, out_w, 3), dtype=np.uint8)
            padded_frame[20:20+h, 20:20+w] = frame

            # Fast alpha blending without BGRA conversion
            padded_float = padded_frame.astype(np.float32)
            result_rgb = alpha_background * padded_float + alpha_overlay * overlay_rgb
            result_frame = result_rgb.astype(np.uint8)

            out.write(result_frame)
            frame_count += 1
            pbar.update(1)

            # Performance monitoring
            if frame_count % 100 == 0:
                elapsed = time.time() - processing_start
                current_fps = frame_count / elapsed if elapsed > 0 else 0
                pbar.set_postfix({"FPS": f"{current_fps:.1f}"})

            continue

        # GPU path (if CUDA is available)
        padded_frame = add_padding_with_transparency(frame, use_cuda=use_cuda)
        result = overlay_image_alpha(padded_frame, overlay_resized, use_cuda=use_cuda)

        if use_cuda:
            try:
                gpu_result = cv2.cuda_GpuMat()
                gpu_result.upload(result)
                gpu_result_bgr = cv2.cuda.cvtColor(gpu_result, cv2.COLOR_BGRA2BGR)
                result_bgr = gpu_result_bgr.download()
            except Exception:
                result_bgr = cv2.cvtColor(result, cv2.COLOR_BGRA2BGR)
        else:
            result_bgr = cv2.cvtColor(result, cv2.COLOR_BGRA2BGR)

        out.write(result_bgr)
        frame_count += 1
        pbar.update(1)

        if frame_count % 100 == 0:
            elapsed = time.time() - processing_start
            current_fps = frame_count / elapsed if elapsed > 0 else 0
            pbar.set_postfix({"FPS": f"{current_fps:.1f}"})

    pbar.close()
    cap.release()
    out.release()

    total_time = time.time() - processing_start
    avg_fps = frame_count / total_time if total_time > 0 else 0
    print(f"✅ Processed {frame_count} frames in {total_time:.1f}s (avg {avg_fps:.1f} FPS)")


def main():
    all_metadata = []
    parent_dir = None

    while True:
        original_shots_path = input("\n📁 Enter path to 'original_shots': ").strip()
        if os.path.isdir(original_shots_path):
            parent_dir = os.path.abspath(os.path.join(original_shots_path, os.pardir))
            break
        else:
            print("❌ Invalid folder path. Try again.")

    while True:
        print("\nChoose a task:\n"
              "1: Extract clips from videos\n"
              "2: Organize selected clips\n"
              "3: Important: Format correction\n"
              "4: Merge product wise\n"
              "5: Final merge all in one\n"
              "6: Merge clip folder wise\n"
              "7: cut orginal with everything\n"
              "8: Overlay and padding\n"
              "exit: Exit the tool")
        choice = input("Enter your choice: ").strip()

        if choice == '1':
            start_time = time.time()
            clips_dir = os.path.join(parent_dir, "clips")
            first_frames_dir = os.path.join(parent_dir, "shots_first_frames")
            os.makedirs(clips_dir, exist_ok=True)
            os.makedirs(first_frames_dir, exist_ok=True)

            video_files = sorted([
                f for f in os.listdir(original_shots_path)
                if os.path.isfile(os.path.join(original_shots_path, f)) and f.lower().endswith(('.mp4', '.mov'))
            ])

            all_metadata = []
            video_id_map = {}  # ← NEW: video_id → original video filename
            global_clip_index = 1

            for vid_index, video_file in enumerate(video_files):
                video_path = os.path.join(original_shots_path, video_file)
                metadata, global_clip_index = process_video(
                    video_path, clips_dir, first_frames_dir, global_clip_index, vid_index)
                all_metadata.extend(metadata)

                # Add mapping for this video_id
                video_id = f"vid{vid_index+1:04d}"
                video_id_map[video_id] = video_file

            metadata_path = os.path.join(clips_dir, "clips_metadata.json")
            with open(metadata_path, "w") as f:
                json.dump(all_metadata, f, indent=2)

            # Save the video_id to filename map
            video_id_map_path = os.path.join(clips_dir, "video_id_map.json")
            with open(video_id_map_path, "w") as f:
                json.dump(video_id_map, f, indent=2)

            print("🔍 Clustering all clips from all videos...")
            groups = cluster_clips_by_histogram(all_metadata, first_frames_dir)
            save_group_representative_images(
                groups=groups,
                metadata=all_metadata,
                first_frames_dir=os.path.join(parent_dir, "shots_first_frames"),
                output_dir=os.path.join(parent_dir, "clustered_first_frames")
            )

            print(f"✅ Total clusters formed: {len(groups)}")

            combined_json_path = os.path.join(clips_dir, "all_clips_metadata_and_clusters.json")
            with open(combined_json_path, "w") as f:
                json.dump({
                    "metadata": all_metadata,
                    "clusters": groups,
                    "video_id_map": video_id_map
                }, f, indent=4)

            # ✅ Save just groups for Task 2
            groups_json_path = os.path.join(clips_dir, "groups.json")
            with open(groups_json_path, "w") as f:
                json.dump(groups, f, indent=2)

            print(f"\n📦 Total clips: {len(all_metadata)}")
            print(f"🖼️ First frames saved in: {first_frames_dir}")
            print(f"📜 Metadata saved at: {metadata_path}")
            print(f"🗺️ Video ID Map saved at: {video_id_map_path}")
            print(f"🧩 Cluster groups saved at: {groups_json_path}")
            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '2':
            start_time = time.time()
            clips_dir = os.path.join(parent_dir, "clips")

            task2(clips_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '3':
            start_time = time.time()

            task3(parent_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '4':
            start_time = time.time()

            task4(parent_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '5':
            start_time = time.time()

            task5(parent_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")
        
        elif choice == '6':
            start_time = time.time()

            task6(parent_dir)

            print(f"⏱️ Time taken: {time.time() - start_time:.2f} seconds")
            print("✅ All done!")

        elif choice == '7':
            start_time = float(input("Start second (e.g. 0): "))
            duration = float(input("Duration (e.g. 10): "))
            input_video = input("Input video path with name: ")
            output_path = input("Output path with name: ")
            extract_clip_original(input_video, start_time, duration, output_path)

        elif choice == '8':
            # Use the provided test video path
            test_video_path = r"D:\testing\marumagal\original_video"

            if not os.path.exists(test_video_path):
                print(f"❌ Test video path not found: {test_video_path}")
                print("Please ensure the path exists and contains video files.")
                continue

            input_dir = os.path.abspath(test_video_path)
            overlay_path = os.path.join(os.path.dirname(__file__), "suntv_logo.png")
            output_dir = os.path.join(input_dir, "overlayed_logo")

            if not os.path.exists(overlay_path):
                print("❌ Overlay image 'suntv_logo.png' not found in script folder.")
                print("Creating a sample overlay image for testing...")
                # Create a simple test overlay image
                test_overlay = np.zeros((100, 200, 4), dtype=np.uint8)
                test_overlay[:, :, 0] = 255  # Blue channel
                test_overlay[:, :, 3] = 128  # Alpha channel (semi-transparent)
                cv2.putText(test_overlay, "TEST LOGO", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255, 255), 2)
                cv2.imwrite(overlay_path, test_overlay)
                print(f"✅ Created test overlay at: {overlay_path}")

            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            overlay_img = cv2.imread(overlay_path, cv2.IMREAD_UNCHANGED)
            if overlay_img is None:
                print("❌ Failed to load overlay image.")
                continue
            if overlay_img.shape[2] != 4:
                print("⚠️ Overlay image doesn't have alpha channel, adding one...")
                overlay_img = cv2.cvtColor(overlay_img, cv2.COLOR_BGR2BGRA)

            # Check GPU availability (PyTorch + OpenCV)
            gpu_info = check_gpu_availability()
            use_cuda = gpu_info['torch_cuda'] or gpu_info['opencv_cuda']

            # Setup processing mode
            if gpu_info['torch_cuda']:
                print("🚀 Using PyTorch GPU acceleration!")
                print(f"   Device: {gpu_info['device_name']}")
                processing_mode = "PyTorch GPU"
            elif gpu_info['opencv_cuda']:
                print("� Using OpenCV CUDA acceleration!")
                processing_mode = "OpenCV CUDA"
            else:
                print("🔧 Using optimized CPU processing...")
                # Set optimal number of threads
                num_threads = min(cv2.getNumberOfCPUs(), 8)
                cv2.setNumThreads(num_threads)
                print(f"   Using {num_threads} CPU threads")

                # Enable optimized CPU instructions
                try:
                    cv2.setUseOptimized(True)
                    print("   CPU optimizations enabled")
                except:
                    pass
                processing_mode = "CPU Optimized"

            print(f"⚙️  Processing mode: {processing_mode}")

            videos = glob.glob(os.path.join(input_dir, "*.mp4")) + glob.glob(os.path.join(input_dir, "*.mov"))

            if not videos:
                print(f"❌ No video files found in: {input_dir}")
                print("Supported formats: .mp4, .mov")
                continue

            print(f"📹 Found {len(videos)} video(s) to process")

            # Process videos with timing
            start_time = time.time()
            for i, video_path in enumerate(videos):
                filename = os.path.basename(video_path)
                output_path = os.path.join(output_dir, filename)
                print(f"\n🎬 Processing ({i+1}/{len(videos)}): {filename}")

                video_start = time.time()
                process_video_overlay(video_path, overlay_img, output_path, use_cuda)
                video_time = time.time() - video_start

                print(f"✅ Completed: {filename} (took {video_time:.1f}s)")

            total_time = time.time() - start_time
            print(f"\n🎉 All videos processed in {total_time:.1f}s!")
            print(f"📁 Output saved to: {output_dir}")

            if not use_cuda:
                print("\n💡 To enable GPU acceleration:")
                print("   1. Install PyTorch with CUDA: pip install torch torchvision --index-url https://download.pytorch.org/whl/cu121")
                print("   2. Or install OpenCV with CUDA support")
                print("   3. Current CPU processing is already optimized!")

        elif choice.lower() == 'exit':
            print("👋 Exiting the tool.")
            break

        else:
            print("❌ Invalid choice. Please enter 1, 2, 3, 4 or 'exit'.")

if __name__ == "__main__":
    main()
